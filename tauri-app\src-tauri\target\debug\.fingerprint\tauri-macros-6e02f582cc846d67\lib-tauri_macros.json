{"rustc": 16591470773350601817, "features": "[\"compression\", \"shell-scope\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"shell-scope\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 2916816621146459921, "deps": [[2713742371683562785, "syn", false, 9265043602832645842], [3060637413840920116, "proc_macro2", false, 17854706098805066986], [8292277814562636972, "tauri_utils", false, 7878931743930959709], [13077543566650298139, "heck", false, 1272924532536600590], [17492769205600034078, "tauri_codegen", false, 10023016365332654593], [17990358020177143287, "quote", false, 11626565584918915378]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-6e02f582cc846d67\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}