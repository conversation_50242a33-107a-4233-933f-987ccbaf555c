// ==UserScript==
// @name         百度网盘助手
// @namespace    http://tampermonkey.net/
// @version      0.1
// @description  百度网盘工具
// <AUTHOR> name
// @match        *://pan.baidu.com/*
// @grant        GM_addStyle
// @grant        GM_xmlhttpRequest
// @grant        GM_setValue
// @grant        GM_getValue
// ==/UserScript==

(function() {
    'use strict';

    // 配置信息
    const CONFIG = {
        API_BASE: 'https://dp.wpurl.cc/api/v1/user',
    };

    // 生成随机参数
    function generateRandParams() {
        const generateHex = (length) => {
            const chars = '0123456789abcdef';
            let result = '';
            for (let i = 0; i < length; i++) {
                result += chars[Math.floor(Math.random() * chars.length)];
            }
            return result;
        };

        return {
            rand1: generateHex(40), // 40位十六进制
            rand2: generateHex(32), // 32位十六进制
            rand3: generateHex(40)  // 40位十六进制
        };
    }

    // 添加样式
    GM_addStyle(`
        @keyframes slideIn {
            from {
                transform: translate(-50%, -40%);
                opacity: 0;
            }
            to {
                transform: translate(-50%, -50%);
                opacity: 1;
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .pan-parser-container {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 
                0 10px 30px rgba(0,0,0,0.1),
                0 1px 8px rgba(0,0,0,0.05),
                inset 0 0 0 1px rgba(255,255,255, 0.4);
            width: 400px;
            display: none;
            z-index: 9999;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            animation: slideIn 0.3s ease-out;
            border: 1px solid rgba(255,255,255, 0.2);
        }

        .pan-parser-container.show {
            display: block;
        }

        .pan-parser-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #eef2f7;
        }

        .pan-parser-title {
            font-size: 24px;
            font-weight: 600;
            color: #2d3748;
            letter-spacing: -0.5px;
        }

        .pan-parser-close {
            cursor: pointer;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: #f7fafc;
            color: #4a5568;
            font-size: 20px;
            transition: all 0.2s ease;
        }

        .pan-parser-close:hover {
            background: #edf2f7;
            color: #2d3748;
            transform: rotate(90deg);
        }

        .pan-parser-content {
            margin-bottom: 20px;
        }

        .pan-parser-input-group {
            margin-bottom: 20px;
        }

        .pan-parser-input-group label {
            display: block;
            margin-bottom: 8px;
            color: #4a5568;
            font-weight: 500;
            font-size: 14px;
        }

        .pan-parser-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            background: #f8fafc;
            color: #2d3748;
            font-size: 15px;
            transition: all 0.2s ease;
            outline: none;
        }

        .pan-parser-input:focus {
            border-color: #4299e1;
            background: #fff;
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.15);
        }

        .pan-parser-button {
            width: 100%;
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 10px;
            font-weight: 600;
            font-size: 15px;
            cursor: pointer;
            transition: all 0.2s ease;
            outline: none;
            box-shadow: 0 4px 6px rgba(50, 50, 93, 0.11), 0 1px 3px rgba(0, 0, 0, 0.08);
        }

        .pan-parser-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 7px 14px rgba(50, 50, 93, 0.1), 0 3px 6px rgba(0, 0, 0, 0.08);
            background: linear-gradient(135deg, #3182ce 0%, #2c5282 100%);
        }

        .pan-parser-button:active {
            transform: translateY(1px);
        }

        .pan-parser-button:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .pan-parser-status {
            margin-top: 20px;
            padding: 16px;
            border-radius: 12px;
            font-size: 14px;
            line-height: 1.6;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            box-shadow: 
                0 4px 6px rgba(0, 0, 0, 0.05),
                inset 0 0 0 1px rgba(255, 255, 255, 0.4);
        }

        .pan-parser-status.success {
            background: rgba(240, 255, 244, 0.9);
            color: #2f855a;
            border: 1px solid rgba(198, 246, 213, 0.4);
        }

        .pan-parser-status.error {
            background: rgba(255, 245, 245, 0.9);
            color: #c53030;
            border: 1px solid rgba(254, 178, 178, 0.4);
        }

        .pan-parser-status .status-section {
            margin: 8px 0;
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .pan-parser-status .status-title {
            font-weight: 600;
            color: #4a5568;
            margin-bottom: 4px;
        }

        .pan-parser-status .status-value {
            color: #2d3748;
        }

        .pan-parser-trigger {
            position: fixed;
            top: 100px;
            right: 20px;
            z-index: 9998;
            padding: 12px 24px;
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            font-size: 15px;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 4px 6px rgba(50, 50, 93, 0.11), 0 1px 3px rgba(0, 0, 0, 0.08);
        }

        .pan-parser-trigger:hover {
            transform: translateY(-1px);
            box-shadow: 0 7px 14px rgba(50, 50, 93, 0.1), 0 3px 6px rgba(0, 0, 0, 0.08);
        }

        .pan-parser-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(26, 32, 44, 0.4);
            backdrop-filter: blur(4px);
            display: none;
            z-index: 9997;
            animation: fadeIn 0.2s ease-out;
        }

        .pan-parser-overlay.show {
            display: block;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .pan-parser-loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #fff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 0.6s linear infinite;
            margin-right: 8px;
        }
    `);

    class UI {
        constructor() {
            this.createElements();
            this.bindEvents();
            this.cardKey = GM_getValue('cardKey', '');
        }

        createElements() {
            // 创建触发按钮
            const triggerButton = document.createElement('button');
            triggerButton.className = 'pan-parser-trigger';
            triggerButton.innerHTML = '<span style="margin-right:8px;">🔑</span>验证卡密';
            document.body.appendChild(triggerButton);

            // 创建遮罩层
            const overlay = document.createElement('div');
            overlay.className = 'pan-parser-overlay';
            document.body.appendChild(overlay);

            // 创建主容器
            const container = document.createElement('div');
            container.className = 'pan-parser-container';
            container.innerHTML = `
                <div class="pan-parser-header">
                    <div class="pan-parser-title">卡密验证</div>
                    <div class="pan-parser-close">×</div>
                </div>
                <div class="pan-parser-content">
                    <div class="pan-parser-input-group">
                        <label>卡密</label>
                        <input type="text" class="pan-parser-input" id="cardKey" 
                               placeholder="请输入卡密" value="${this.cardKey}">
                    </div>
                    <button class="pan-parser-button" id="verifyCard">
                        验证卡密
                    </button>
                    <div id="statusArea"></div>
                </div>
            `;
            document.body.appendChild(container);

            // 保存引用
            this.elements = {
                trigger: triggerButton,
                overlay: overlay,
                container: container,
                closeBtn: container.querySelector('.pan-parser-close'),
                verifyBtn: container.querySelector('#verifyCard'),
                cardKeyInput: container.querySelector('#cardKey'),
                statusArea: container.querySelector('#statusArea')
            };
        }

        bindEvents() {
            // 打开面板
            this.elements.trigger.addEventListener('click', () => {
                this.showPanel();
            });

            // 关闭面板
            this.elements.closeBtn.addEventListener('click', () => {
                this.hidePanel();
            });

            // 点击遮罩层关闭
            this.elements.overlay.addEventListener('click', () => {
                this.hidePanel();
            });

            // 验证卡密
            this.elements.verifyBtn.addEventListener('click', () => {
                this.handleVerifyCard();
            });

            // 输入框回车触发验证
            this.elements.cardKeyInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.handleVerifyCard();
                }
            });
        }

        showPanel() {
            this.elements.overlay.classList.add('show');
            this.elements.container.classList.add('show');
            this.elements.cardKeyInput.focus();
        }

        hidePanel() {
            this.elements.overlay.classList.remove('show');
            this.elements.container.classList.remove('show');
        }

        showLoading() {
            this.elements.verifyBtn.innerHTML = `
                <span class="pan-parser-loading"></span>
                验证中...
            `;
            this.elements.verifyBtn.disabled = true;
        }

        hideLoading() {
            this.elements.verifyBtn.innerHTML = '验证卡密';
            this.elements.verifyBtn.disabled = false;
        }

        showStatus(message, type = 'error') {
            this.elements.statusArea.innerHTML = `
                <div class="pan-parser-status ${type}">
                    ${message}
                </div>
            `;
        }

        async handleVerifyCard() {
            const cardKey = this.elements.cardKeyInput.value.trim();
            if (!cardKey) {
                this.showStatus('请输入卡密');
                return;
            }

            this.showLoading();
            try {
                // 生成随机参数
                const randParams = generateRandParams();
                
                // 构建URL参数
                const queryParams = new URLSearchParams({
                    token: cardKey,
                    ...randParams
                }).toString();
                
                // 使用GM_xmlhttpRequest发送验证请求
                const response = await new Promise((resolve, reject) => {
                    GM_xmlhttpRequest({
                        method: 'GET',
                        url: `${CONFIG.API_BASE}/token?${queryParams}`,
                        headers: {
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                            'Accept': 'application/json'
                        },
                        onload: (response) => {
                            console.log('API原始响应:', response); // 添加调试日志
                            
                            if (!response.responseText) {
                                reject(new Error('响应内容为空'));
                                return;
                            }

                            try {
                                // 尝试解析响应
                                const contentType = response.responseHeaders.split('\n')
                                    .find(line => line.toLowerCase().startsWith('content-type:'));
                                
                                console.log('响应Content-Type:', contentType); // 添加调试日志
                                
                                // 检查响应状态码
                                if (response.status !== 200) {
                                    reject(new Error(`服务器返回错误状态码: ${response.status}`));
                                    return;
                                }

                                const data = JSON.parse(response.responseText);
                                console.log('解析后的响应数据:', data); // 添加调试日志
                                resolve(data);
                            } catch (error) {
                                console.error('响应解析错误:', error); // 添加错误日志
                                console.log('原始响应内容:', response.responseText); // 记录原始响应
                                reject(new Error(`响应解析失败: ${error.message}`));
                            }
                        },
                        onerror: (error) => {
                            console.error('请求错误:', error); // 添加错误日志
                            reject(new Error('网络请求失败'));
                        }
                    });
                });

                console.log('完整响应数据:', response); // 添加调试日志

                if (response.code === 200) {
                    GM_setValue('cardKey', cardKey);
                    
                    // 格式化显示关键信息
                    const data = response.data;
                    const formatSize = (bytes) => {
                        const units = ['B', 'KB', 'MB', 'GB', 'TB'];
                        let size = bytes;
                        let unitIndex = 0;
                        while (size >= 1024 && unitIndex < units.length - 1) {
                            size /= 1024;
                            unitIndex++;
                        }
                        return `${size.toFixed(2)} ${units[unitIndex]}`;
                    };

                    this.showStatus(
                        `<div style="text-align: center; font-size: 16px; margin-bottom: 15px;">
                            ✅ 卡密验证成功！
                        </div>
                        <div class="status-section">
                            <div class="status-title">📊 使用情况</div>
                            <div class="status-value">
                                总次数：${data.count} 次<br>
                                已用次数：${data.count - data.remaining_count} 次<br>
                                剩余次数：<span style="color: #2f855a; font-weight: 600">${data.remaining_count} 次</span>
                            </div>
                        </div>
                        <div class="status-section">
                            <div class="status-title">💾 空间配额</div>
                            <div class="status-value">
                                总空间：${formatSize(data.size)}<br>
                                剩余空间：<span style="color: #2f855a; font-weight: 600">${formatSize(data.remaining_size)}</span>
                            </div>
                        </div>
                        <div class="status-section">
                            <div class="status-title">⏰ 时间信息</div>
                            <div class="status-value">
                                最后使用：${data.used_at}<br>
                                过期时间：<span style="color: #2f855a; font-weight: 600">${data.expires_at}</span>
                            </div>
                        </div>`,
                        'success'
                    );
                    
                    // 延长显示时间，方便查看
                    setTimeout(() => this.hidePanel(), 5000);
                } else {
                    this.showStatus(`验证失败: ${response.message || '未知错误'}`);
                }
            } catch (error) {
                console.error('验证过程错误:', error); // 添加错误日志
                this.showStatus(`验证请求失败: ${error.message}`);
            } finally {
                this.hideLoading();
            }
        }
    }

    // 初始化UI
    new UI();
})(); 