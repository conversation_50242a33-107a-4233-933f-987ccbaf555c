{"rustc": 16591470773350601817, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"default\", \"futures-macro\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 13318305459243126790, "path": 10070852354549691761, "deps": [[1615478164327904835, "pin_utils", false, 18430932456481680242], [1906322745568073236, "pin_project_lite", false, 17990441821848971746], [6955678925937229351, "slab", false, 1658408689604310943], [7620660491849607393, "futures_core", false, 12374657996723424010], [10565019901765856648, "futures_macro", false, 1776519169928639893], [16240732885093539806, "futures_task", false, 194293613543405041]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-352664ceee1439bf\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}