{"rustc": 16591470773350601817, "features": "[\"std\"]", "declared_features": "[\"async-await\", \"attributes\", \"default\", \"log\", \"log-always\", \"max_level_debug\", \"max_level_error\", \"max_level_info\", \"max_level_off\", \"max_level_trace\", \"max_level_warn\", \"release_max_level_debug\", \"release_max_level_error\", \"release_max_level_info\", \"release_max_level_off\", \"release_max_level_trace\", \"release_max_level_warn\", \"std\", \"tracing-attributes\", \"valuable\"]", "target": 5568135053145998517, "profile": 6355579909791343455, "path": 13092852071057884430, "deps": [[1906322745568073236, "pin_project_lite", false, 17990441821848971746], [3424551429995674438, "tracing_core", false, 1335595449096740706]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-e3df4457decb858d\\dep-lib-tracing", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}