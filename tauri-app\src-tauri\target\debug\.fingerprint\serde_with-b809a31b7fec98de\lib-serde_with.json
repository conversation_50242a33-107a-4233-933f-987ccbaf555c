{"rustc": 16591470773350601817, "features": "[\"alloc\", \"default\", \"macros\", \"std\"]", "declared_features": "[\"alloc\", \"base64\", \"chrono\", \"chrono_0_4\", \"default\", \"guide\", \"hashbrown_0_14\", \"hashbrown_0_15\", \"hex\", \"indexmap\", \"indexmap_1\", \"indexmap_2\", \"json\", \"macros\", \"schemars_0_8\", \"std\", \"time_0_3\"]", "target": 10448421281463538527, "profile": 5290030462671737236, "path": 2206050493830264902, "deps": [[6158493786865284961, "serde_with_macros", false, 413387586141031780], [9689903380558560274, "serde", false, 3990983680525661946], [16257276029081467297, "serde_derive", false, 7792210141377979093]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\serde_with-b809a31b7fec98de\\dep-lib-serde_with", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}