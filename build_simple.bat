@echo off
echo 正在打包百度网盘解析工具...
echo.

REM 检查是否安装了PyInstaller
python -c "import PyInstaller" 2>nul
if errorlevel 1 (
    echo 正在安装PyInstaller...
    pip install pyinstaller
)

REM 检查是否安装了requests
python -c "import requests" 2>nul
if errorlevel 1 (
    echo 正在安装requests...
    pip install requests
)

echo.
echo 开始打包...
echo.

REM 使用PyInstaller打包（不使用图标）
pyinstaller --onefile --windowed --name "百度网盘解析工具" baidu_pan_ui.py

echo.
echo 打包完成！
echo 可执行文件位置: dist\百度网盘解析工具.exe
echo.
echo 注意事项：
echo 1. 首次运行时请确保网络连接正常
echo 2. 如需使用IDM下载功能，请先安装IDM
echo 3. 程序会自动检测IDM安装路径
echo.
pause
