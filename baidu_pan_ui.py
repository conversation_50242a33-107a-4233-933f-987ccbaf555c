import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, filedialog
import requests
import secrets
import json
from datetime import datetime
import threading
import subprocess
import os


class BaiduPanUI:
    def __init__(self, root):
        self.root = root
        self.root.title("百度网盘解析工具")
        self.root.geometry("1000x700")
        
        # 当前文件列表数据
        self.current_files = []
        self.current_path = "/"

        # 存储API响应中的关键信息
        self.uk = None
        self.shareid = None
        self.randsk = None

        # IDM配置
        self.idm_path = self.find_idm_path()
        self.download_folder = os.path.join(os.path.expanduser("~"), "Downloads")
        
        self.create_widgets()
        self.check_idm_status()
        
    def create_widgets(self):
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
        # 输入区域
        ttk.Label(main_frame, text="分享链接:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.url_entry = ttk.Entry(main_frame, width=60)
        self.url_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=2)
        self.url_entry.insert(0, "https://pan.baidu.com/s/1mVe3LrL7US1MF5jICzmycg?")
        
        ttk.Label(main_frame, text="提取码:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.pwd_entry = ttk.Entry(main_frame, width=20)
        self.pwd_entry.grid(row=1, column=1, sticky=tk.W, pady=2)
        self.pwd_entry.insert(0, "1111")
        
        ttk.Label(main_frame, text="解析卡密:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.token_entry = ttk.Entry(main_frame, width=30)
        self.token_entry.grid(row=2, column=1, sticky=tk.W, pady=2)
        self.token_entry.insert(0, "thAfgmFnC0uahCK3")
        
        # 当前路径显示
        path_frame = ttk.Frame(main_frame)
        path_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        path_frame.columnconfigure(1, weight=1)
        
        ttk.Label(path_frame, text="当前路径:").grid(row=0, column=0, sticky=tk.W)
        self.path_label = ttk.Label(path_frame, text="/", background="white", relief="sunken")
        self.path_label.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(5, 0))
        
        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=2, sticky=tk.E, pady=5)
        
        self.get_list_btn = ttk.Button(button_frame, text="获取文件列表", command=self.get_file_list)
        self.get_list_btn.grid(row=0, column=0, padx=2)
        
        self.back_btn = ttk.Button(button_frame, text="返回上级", command=self.go_back, state="disabled")
        self.back_btn.grid(row=0, column=1, padx=2)
        
        # 文件列表区域
        list_frame = ttk.Frame(main_frame)
        list_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        # 创建Treeview
        columns = ("name", "type", "size", "mtime", "file_data")
        self.tree = ttk.Treeview(list_frame, columns=columns, show="tree headings", height=15)
        
        # 设置列标题
        self.tree.heading("#0", text="选择")
        self.tree.heading("name", text="文件名")
        self.tree.heading("type", text="类型")
        self.tree.heading("size", text="大小")
        self.tree.heading("mtime", text="修改时间")
        
        # 设置列宽
        self.tree.column("#0", width=50, minwidth=50)
        self.tree.column("name", width=400, minwidth=200)
        self.tree.column("type", width=80, minwidth=80)
        self.tree.column("size", width=100, minwidth=100)
        self.tree.column("mtime", width=150, minwidth=150)
        self.tree.column("file_data", width=0, minwidth=0)  # 隐藏的数据列

        # 隐藏file_data列
        self.tree["displaycolumns"] = ("name", "type", "size", "mtime")
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 绑定双击事件
        self.tree.bind("<Double-1>", self.on_item_double_click)
        
        # 操作按钮区域
        action_frame = ttk.Frame(main_frame)
        action_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        self.parse_btn = ttk.Button(action_frame, text="解析选中文件", command=self.parse_selected_files)
        self.parse_btn.grid(row=0, column=0, padx=2)
        
        self.select_all_btn = ttk.Button(action_frame, text="全选", command=self.select_all)
        self.select_all_btn.grid(row=0, column=1, padx=2)
        
        self.clear_selection_btn = ttk.Button(action_frame, text="清除选择", command=self.clear_selection)
        self.clear_selection_btn.grid(row=0, column=2, padx=2)

        self.copy_links_btn = ttk.Button(action_frame, text="复制所有链接", command=self.copy_all_links)
        self.copy_links_btn.grid(row=0, column=3, padx=2)

        self.download_folder_btn = ttk.Button(action_frame, text="下载选中文件夹", command=self.download_selected_folders)
        self.download_folder_btn.grid(row=0, column=4, padx=2)

        # 使用说明
        usage_frame = ttk.Frame(main_frame)
        usage_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=2)

        usage_label = ttk.Label(usage_frame, text="💡 使用说明: 选中文件夹后点击'下载选中文件夹'，将自动创建以文件夹名命名的目录并下载所有文件",
                               foreground="blue", font=("", 8))
        usage_label.grid(row=0, column=0, sticky=tk.W)

        # 存储解析出的下载链接
        self.parsed_links = []
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="操作日志", padding="5")
        log_frame.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置主框架的行权重
        main_frame.rowconfigure(6, weight=1)

        # IDM设置区域
        idm_frame = ttk.LabelFrame(main_frame, text="IDM设置", padding="5")
        idm_frame.grid(row=7, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        idm_frame.columnconfigure(1, weight=1)

        ttk.Label(idm_frame, text="IDM路径:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.idm_path_var = tk.StringVar(value=self.idm_path or "未找到IDM")
        self.idm_path_label = ttk.Label(idm_frame, textvariable=self.idm_path_var, background="white", relief="sunken")
        self.idm_path_label.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(5, 5))

        self.browse_idm_btn = ttk.Button(idm_frame, text="浏览", command=self.browse_idm_path)
        self.browse_idm_btn.grid(row=0, column=2, padx=2)

        ttk.Label(idm_frame, text="下载目录:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.download_folder_var = tk.StringVar(value=self.download_folder)
        self.download_folder_label = ttk.Label(idm_frame, textvariable=self.download_folder_var, background="white", relief="sunken")
        self.download_folder_label.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(5, 5))

        self.browse_folder_btn = ttk.Button(idm_frame, text="浏览", command=self.browse_download_folder)
        self.browse_folder_btn.grid(row=1, column=2, padx=2)

        # 自动下载选项
        self.auto_download_var = tk.BooleanVar(value=True)
        self.auto_download_cb = ttk.Checkbutton(idm_frame, text="解析后自动调用IDM下载", variable=self.auto_download_var)
        self.auto_download_cb.grid(row=2, column=0, columnspan=2, sticky=tk.W, pady=5)

        # 测试IDM按钮
        self.test_idm_btn = ttk.Button(idm_frame, text="测试IDM", command=self.test_idm)
        self.test_idm_btn.grid(row=2, column=2, padx=2)

        # 进度条
        self.progress_frame = ttk.Frame(main_frame)
        self.progress_frame.grid(row=8, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        self.progress_frame.columnconfigure(0, weight=1)

        self.progress_label = ttk.Label(self.progress_frame, text="")
        self.progress_label.grid(row=0, column=0, sticky=tk.W)

        self.progress_bar = ttk.Progressbar(self.progress_frame, mode='determinate')
        self.progress_bar.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=2)

        # 初始时隐藏进度条
        self.progress_frame.grid_remove()
        
    def find_idm_path(self):
        """查找IDM安装路径"""
        possible_paths = [
            r"C:\Program Files (x86)\Internet Download Manager\IDMan.exe",
            r"C:\Program Files\Internet Download Manager\IDMan.exe",
            r"D:\Program Files (x86)\Internet Download Manager\IDMan.exe",
            r"D:\Program Files\Internet Download Manager\IDMan.exe",
        ]

        for path in possible_paths:
            if os.path.exists(path):
                return path
        return None

    def browse_idm_path(self):
        """浏览选择IDM路径"""
        filename = filedialog.askopenfilename(
            title="选择IDM程序",
            filetypes=[("可执行文件", "*.exe"), ("所有文件", "*.*")]
        )
        if filename:
            self.idm_path = filename
            self.idm_path_var.set(filename)

    def browse_download_folder(self):
        """浏览选择下载文件夹"""
        folder = filedialog.askdirectory(title="选择下载文件夹")
        if folder:
            self.download_folder = folder
            self.download_folder_var.set(folder)

    def check_idm_status(self):
        """检查IDM状态并显示提示"""
        if self.idm_path and os.path.exists(self.idm_path):
            self.log(f"✅ IDM已找到: {self.idm_path}")
            self.log(f"📁 下载目录: {self.download_folder}")
        else:
            self.log("⚠️  未找到IDM，请手动设置IDM路径")
            self.log("💡 提示: 可以使用'复制所有链接'功能手动下载")

    def show_progress(self, current, total, message=""):
        """显示进度"""
        self.progress_frame.grid()
        self.progress_bar['maximum'] = total
        self.progress_bar['value'] = current

        if message:
            self.progress_label.config(text=f"{message} ({current}/{total})")
        else:
            self.progress_label.config(text=f"进度: {current}/{total}")

        self.root.update()

    def hide_progress(self):
        """隐藏进度条"""
        self.progress_frame.grid_remove()
        self.root.update()

    def test_idm(self):
        """测试IDM是否能正常工作"""
        if not self.idm_path or not os.path.exists(self.idm_path):
            messagebox.showerror("错误", "IDM路径未设置或文件不存在")
            return

        # 使用一个测试URL来测试IDM
        test_url = "https://www.baidu.com/img/bd_logo1.png"
        test_filename = "test_download.png"

        self.log("🧪 开始测试IDM...")
        if self.call_idm_download(test_url, test_filename):
            messagebox.showinfo("成功", "IDM测试成功！请检查IDM是否弹出下载对话框。")
        else:
            messagebox.showerror("失败", "IDM测试失败，请检查IDM路径是否正确。")

    def call_idm_download(self, download_url, filename, file_path=None):
        """调用IDM下载文件"""
        if not self.idm_path or not os.path.exists(self.idm_path):
            self.log(f"❌ IDM未找到，无法下载 {filename}")
            return False

        # 确定下载路径
        download_path = self.download_folder

        # 如果提供了文件路径，创建相应的子目录结构
        if file_path and file_path != filename:
            # 从文件路径中提取目录结构
            path_parts = file_path.split('/')
            if len(path_parts) > 1:
                # 移除文件名，保留目录路径
                dir_structure = '/'.join(path_parts[:-1])
                # 移除开头的斜杠和根目录部分
                if dir_structure.startswith('/'):
                    dir_structure = dir_structure[1:]

                # 创建完整的下载路径
                if dir_structure:
                    download_path = os.path.join(self.download_folder, dir_structure)

                    # 确保目录存在
                    try:
                        os.makedirs(download_path, exist_ok=True)
                        self.log(f"   📁 创建目录: {download_path}")
                    except Exception as e:
                        self.log(f"   ⚠️  创建目录失败: {str(e)}")
                        download_path = self.download_folder

        try:
            # IDM命令行参数
            # /d URL - 下载链接
            # /p 路径 - 下载路径
            # /f 文件名 - 文件名
            # /a - 自动开始下载
            # /n - 静默模式

            cmd = [
                self.idm_path,
                "/d", download_url,
                "/p", download_path,
                "/f", filename,
                "/a"
            ]

            self.log(f"🚀 调用IDM下载: {filename}")
            self.log(f"   IDM命令: {' '.join(cmd)}")

            # 方法1：尝试完整参数
            try:
                process = subprocess.Popen(cmd,
                                         stdout=subprocess.PIPE,
                                         stderr=subprocess.PIPE,
                                         creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0)

                # 等待一小段时间检查是否启动成功
                import time
                time.sleep(0.5)

                if process.poll() is None:
                    self.log(f"   ✅ IDM进程已启动 (PID: {process.pid})")
                    return True
                else:
                    _, stderr = process.communicate()
                    self.log(f"   ❌ 方法1失败，返回码: {process.returncode}")
                    if stderr:
                        self.log(f"   错误信息: {stderr.decode('utf-8', errors='ignore')}")
            except Exception as e1:
                self.log(f"   ❌ 方法1异常: {str(e1)}")

            # 方法2：尝试简化参数
            self.log("   🔄 尝试简化参数...")
            try:
                simple_cmd = [self.idm_path, "/d", download_url, "/a"]
                self.log(f"   简化命令: {' '.join(simple_cmd)}")

                process = subprocess.Popen(simple_cmd)
                time.sleep(0.5)

                if process.poll() is None:
                    self.log(f"   ✅ IDM进程已启动 (PID: {process.pid})")
                    return True
                else:
                    self.log(f"   ❌ 方法2失败，返回码: {process.returncode}")
            except Exception as e2:
                self.log(f"   ❌ 方法2异常: {str(e2)}")

            # 方法3：直接启动IDM让用户手动添加
            self.log("   🔄 尝试直接启动IDM...")
            try:
                process = subprocess.Popen([self.idm_path])
                self.log(f"   ✅ IDM已启动，请手动添加下载链接")
                self.log(f"   📋 下载链接: {download_url}")
                return True
            except Exception as e3:
                self.log(f"   ❌ 方法3异常: {str(e3)}")

            return False

        except Exception as e:
            self.log(f"❌ 调用IDM失败: {str(e)}")
            return False

    def call_idm_download_to_folder(self, download_url, filename, target_folder):
        """调用IDM下载文件到指定文件夹"""
        if not self.idm_path or not os.path.exists(self.idm_path):
            self.log(f"❌ IDM未找到，无法下载 {filename}")
            return False

        try:
            # 确保目标文件夹存在
            os.makedirs(target_folder, exist_ok=True)

            # IDM命令行参数
            cmd = [
                self.idm_path,
                "/d", download_url,
                "/p", target_folder,
                "/f", filename,
                "/a"
            ]

            self.log(f"     🚀 调用IDM下载到: {target_folder}")

            # 方法1：尝试完整参数
            try:
                process = subprocess.Popen(cmd,
                                         stdout=subprocess.PIPE,
                                         stderr=subprocess.PIPE,
                                         creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0)

                import time
                time.sleep(0.5)

                if process.poll() is None:
                    return True
                else:
                    _, stderr = process.communicate()
                    if stderr:
                        self.log(f"     ❌ IDM错误: {stderr.decode('utf-8', errors='ignore')}")
            except Exception:
                pass

            # 方法2：尝试简化参数
            try:
                simple_cmd = [self.idm_path, "/d", download_url, "/a"]
                process = subprocess.Popen(simple_cmd)
                time.sleep(0.5)

                if process.poll() is None:
                    return True
            except Exception:
                pass

            # 方法3：直接启动IDM
            try:
                subprocess.Popen([self.idm_path])
                self.log(f"     ✅ IDM已启动，请手动添加下载链接")
                return True
            except Exception:
                pass

            return False

        except Exception as e:
            self.log(f"❌ 调用IDM失败: {str(e)}")
            return False

    def log(self, message):
        """添加日志信息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update()
        
    def generate_rand_params(self):
        """生成随机参数"""
        rand1 = secrets.token_hex(20)  # 40位十六进制
        rand2 = secrets.token_hex(16)  # 32位十六进制
        rand3 = secrets.token_hex(20)  # 40位十六进制
        return rand1, rand2, rand3
        
    def format_size(self, size_in_bytes):
        """将字节数转换为人类可读的格式"""
        if size_in_bytes == 0:
            return "文件夹"
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size_in_bytes < 1024.0:
                return f"{size_in_bytes:.2f} {unit}"
            size_in_bytes /= 1024.0
        return f"{size_in_bytes:.2f} PB"
        
    def format_time(self, timestamp):
        """将时间戳转换为可读格式"""
        return datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
        
    def get_file_list(self):
        """获取文件列表"""
        def fetch_files():
            try:
                self.get_list_btn.config(state="disabled")
                self.log("开始获取文件列表...")
                
                url = self.url_entry.get().strip()
                pwd = self.pwd_entry.get().strip()
                token = self.token_entry.get().strip()
                
                if not all([url, pwd, token]):
                    messagebox.showerror("错误", "请填写完整的分享链接、提取码和解析卡密")
                    return
                
                base_url = "https://dp.wpurl.cc/api/v1/user/parse"
                rand1, rand2, rand3 = self.generate_rand_params()
                
                headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                    "Content-Type": "application/json",
                    "Accept": "application/json"
                }
                
                file_list_data = {
                    "token": token,
                    "url": url,
                    "pwd": pwd,
                    "dir": self.current_path,
                    "surl": url.split('/')[-1].replace('?', ''),
                    "parse_password": "",
                    "rand1": rand1,
                    "rand2": rand2,
                    "rand3": rand3
                }
                
                self.log(f"请求路径: {self.current_path}")
                
                response = requests.post(
                    f"{base_url}/get_file_list",
                    headers=headers,
                    json=file_list_data,
                    timeout=30
                )
                
                if response.status_code == 200:
                    result = response.json()
                    self.log(f"API响应: {result}")
                    
                    if result.get('code') == 200:
                        data = result.get('data', {})
                        files = data.get('list', [])

                        # 保存关键信息用于后续获取下载链接
                        self.uk = data.get('uk')
                        self.shareid = data.get('shareid')
                        self.randsk = data.get('randsk')

                        self.current_files = files
                        self.update_file_list(files)
                        self.log(f"成功获取 {len(files)} 个文件/文件夹")
                    else:
                        self.log(f"API返回错误: {result.get('message', '未知错误')}")
                        messagebox.showerror("错误", f"获取文件列表失败: {result.get('message', '未知错误')}")
                else:
                    self.log(f"HTTP错误: {response.status_code}")
                    messagebox.showerror("错误", f"请求失败: HTTP {response.status_code}")
                    
            except Exception as e:
                self.log(f"获取文件列表出错: {str(e)}")
                messagebox.showerror("错误", f"获取文件列表出错: {str(e)}")
            finally:
                self.get_list_btn.config(state="normal")
                
        # 在新线程中执行网络请求
        threading.Thread(target=fetch_files, daemon=True).start()
        
    def update_file_list(self, files):
        """更新文件列表显示"""
        # 清空现有项目
        for item in self.tree.get_children():
            self.tree.delete(item)

        # 更新路径显示
        self.path_label.config(text=self.current_path)

        # 启用/禁用返回按钮
        self.back_btn.config(state="normal" if self.current_path != "/" else "disabled")

        # 添加文件项目
        for file_info in files:
            file_type = "📁 文件夹" if file_info.get('is_dir') else "📄 文件"
            size = self.format_size(file_info.get('size', 0))
            mtime = self.format_time(file_info.get('server_mtime', 0))

            self.tree.insert("", "end",
                            text="☐",  # 复选框符号
                            values=(file_info.get('server_filename', ''),
                                  file_type, size, mtime, json.dumps(file_info)))

            # 不需要单独设置file_data，因为已经在values中了
            
    def on_item_double_click(self, _):
        """处理双击事件"""
        item = self.tree.selection()[0] if self.tree.selection() else None
        if not item:
            return

        # 获取文件信息
        values = self.tree.item(item, "values")
        if len(values) < 5:
            return

        file_data_str = values[4]  # file_data在第5列
        if not file_data_str:
            return

        file_info = json.loads(file_data_str)

        # 如果是文件夹，进入该文件夹
        if file_info.get('is_dir'):
            self.current_path = file_info.get('path', '/')
            self.get_file_list()
        else:
            # 如果是文件，切换选择状态
            current_text = self.tree.item(item, "text")
            if current_text == "☐":
                self.tree.item(item, text="☑")
            else:
                self.tree.item(item, text="☐")
                
    def go_back(self):
        """返回上级目录"""
        if self.current_path == "/":
            return
            
        # 计算上级路径
        path_parts = self.current_path.rstrip('/').split('/')
        if len(path_parts) > 1:
            self.current_path = '/'.join(path_parts[:-1]) or '/'
        else:
            self.current_path = '/'
            
        self.get_file_list()
        
    def select_all(self):
        """全选所有项目"""
        for item in self.tree.get_children():
            self.tree.item(item, text="☑")
            
    def clear_selection(self):
        """清除所有选择"""
        for item in self.tree.get_children():
            self.tree.item(item, text="☐")

    def copy_all_links(self):
        """复制所有解析出的下载链接到剪贴板"""
        if not self.parsed_links:
            messagebox.showinfo("提示", "没有可复制的下载链接")
            return

        links_text = "\n".join([f"{link['filename']}: {link['url']}" for link in self.parsed_links])

        try:
            self.root.clipboard_clear()
            self.root.clipboard_append(links_text)
            self.log(f"✅ 已复制 {len(self.parsed_links)} 个下载链接到剪贴板")
            messagebox.showinfo("成功", f"已复制 {len(self.parsed_links)} 个下载链接到剪贴板")
        except Exception as e:
            self.log(f"❌ 复制到剪贴板失败: {str(e)}")
            messagebox.showerror("错误", f"复制失败: {str(e)}")

    def get_all_files_in_folder(self, folder_path, max_depth=5, current_depth=0):
        """递归获取文件夹内的所有文件"""
        if current_depth >= max_depth:
            self.log(f"⚠️  达到最大递归深度 {max_depth}，停止递归: {folder_path}")
            return []

        all_files = []

        try:
            url = self.url_entry.get().strip()
            pwd = self.pwd_entry.get().strip()
            token = self.token_entry.get().strip()

            base_url = "https://dp.wpurl.cc/api/v1/user/parse"
            rand1, rand2, rand3 = self.generate_rand_params()

            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Content-Type": "application/json",
                "Accept": "application/json"
            }

            file_list_data = {
                "token": token,
                "url": url,
                "pwd": pwd,
                "dir": folder_path,
                "surl": url.split('/')[-1].replace('?', ''),
                "parse_password": "",
                "rand1": rand1,
                "rand2": rand2,
                "rand3": rand3
            }

            self.log(f"📂 正在扫描文件夹: {folder_path} (深度: {current_depth})")

            response = requests.post(
                f"{base_url}/get_file_list",
                headers=headers,
                json=file_list_data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 200:
                    data = result.get('data', {})
                    files = data.get('list', [])

                    # 更新关键信息
                    if not self.uk:
                        self.uk = data.get('uk')
                        self.shareid = data.get('shareid')
                        self.randsk = data.get('randsk')

                    for file_info in files:
                        if file_info.get('is_dir'):
                            # 如果是文件夹，递归获取其内容
                            subfolder_path = file_info.get('path', '')
                            self.log(f"  📁 发现子文件夹: {file_info.get('server_filename', '')}")

                            # 递归获取子文件夹内容
                            subfiles = self.get_all_files_in_folder(subfolder_path, max_depth, current_depth + 1)
                            all_files.extend(subfiles)
                        else:
                            # 如果是文件，添加到列表
                            self.log(f"  📄 发现文件: {file_info.get('server_filename', '')} ({self.format_size(file_info.get('size', 0))})")
                            all_files.append(file_info)

                else:
                    self.log(f"❌ 获取文件夹内容失败: {result.get('message', '未知错误')}")
            else:
                self.log(f"❌ HTTP错误: {response.status_code}")

        except Exception as e:
            self.log(f"❌ 扫描文件夹出错: {str(e)}")

        return all_files

    def download_selected_folders(self):
        """下载选中的文件夹"""
        selected_folders = []

        # 获取选中的文件夹
        for item in self.tree.get_children():
            if self.tree.item(item, "text") == "☑":
                values = self.tree.item(item, "values")
                if len(values) >= 5:
                    file_data_str = values[4]
                    if file_data_str:
                        file_info = json.loads(file_data_str)
                        if file_info.get('is_dir'):  # 只处理文件夹
                            selected_folders.append(file_info)

        if not selected_folders:
            messagebox.showwarning("警告", "请先选择要下载的文件夹")
            return

        # 显示选中的文件夹信息
        folder_names = [folder.get('server_filename', '') for folder in selected_folders]
        message = f"将下载以下 {len(selected_folders)} 个文件夹:\n\n" + "\n".join(f"• {name}" for name in folder_names)
        message += f"\n\n每个文件夹将创建独立的下载目录，确定继续吗？"

        result = messagebox.askyesno("确认下载", message)
        if not result:
            return

        def download_folders():
            try:
                self.download_folder_btn.config(state="disabled")
                self.parsed_links.clear()

                total_files_downloaded = 0

                for i, folder_info in enumerate(selected_folders, 1):
                    folder_name = folder_info.get('server_filename', '')
                    folder_path = folder_info.get('path', '')

                    self.log(f"📁 [{i}/{len(selected_folders)}] 开始下载文件夹: {folder_name}")

                    # 创建以文件夹名命名的下载目录
                    folder_download_path = os.path.join(self.download_folder, folder_name)

                    try:
                        os.makedirs(folder_download_path, exist_ok=True)
                        self.log(f"   📂 创建下载目录: {folder_download_path}")
                    except Exception as e:
                        self.log(f"   ❌ 创建目录失败: {str(e)}")
                        continue

                    # 获取文件夹内的所有文件
                    self.log(f"   🔍 扫描文件夹内容...")
                    all_files = self.get_all_files_in_folder(folder_path)

                    if not all_files:
                        self.log(f"   ⚠️  文件夹为空或扫描失败")
                        continue

                    self.log(f"   📊 找到 {len(all_files)} 个文件")

                    # 解析并下载文件夹内的所有文件
                    success_count = self.download_folder_files(all_files, folder_download_path, folder_name)
                    total_files_downloaded += success_count

                    self.log(f"   ✅ 文件夹 '{folder_name}' 完成，成功下载 {success_count} 个文件")
                    self.log("   " + "="*50)

                # 显示总结
                self.log(f"🎉 所有文件夹下载完成！")
                self.log(f"📊 总计成功下载 {total_files_downloaded} 个文件")

                messagebox.showinfo(
                    "下载完成",
                    f"文件夹下载完成！\n\n处理了 {len(selected_folders)} 个文件夹\n总计下载 {total_files_downloaded} 个文件"
                )

            except Exception as e:
                self.log(f"❌ 下载文件夹出错: {str(e)}")
                messagebox.showerror("错误", f"下载文件夹出错: {str(e)}")
            finally:
                self.download_folder_btn.config(state="normal")
                self.hide_progress()

        # 在新线程中执行
        threading.Thread(target=download_folders, daemon=True).start()

    def download_folder_files(self, files_list, folder_download_path, folder_name):
        """下载文件夹内的所有文件到指定目录"""
        try:
            url = self.url_entry.get().strip()
            pwd = self.pwd_entry.get().strip()
            token = self.token_entry.get().strip()

            base_url = "https://dp.wpurl.cc/api/v1/user/parse"

            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Content-Type": "application/json",
                "Accept": "application/json"
            }

            success_count = 0

            for i, file_info in enumerate(files_list, 1):
                filename = file_info.get('server_filename', '')
                file_path = file_info.get('path', '')

                # 更新进度
                self.show_progress(i-1, len(files_list), f"[{folder_name}] 正在解析: {filename}")
                self.log(f"   🔗 [{i}/{len(files_list)}] 正在解析: {filename}")

                try:
                    rand1, rand2, rand3 = self.generate_rand_params()

                    # 获取文件所在的目录路径
                    file_dir = '/'.join(file_path.split('/')[:-1]) or '/'

                    download_data = {
                        "token": token,
                        "url": url,
                        "pwd": pwd,
                        "dir": file_dir,
                        "fs_id": [file_info.get('fs_id')],
                        "randsk": self.randsk or '',
                        "uk": self.uk or '',
                        "shareid": self.shareid or '',
                        "surl": url.split('/')[-1].replace('?', ''),
                        "parse_password": "",
                        "vcode_str": "",
                        "vcode_input": "",
                        "rand1": rand1,
                        "rand2": rand2,
                        "rand3": rand3
                    }

                    response = requests.post(
                        f"{base_url}/get_download_links",
                        headers=headers,
                        json=download_data,
                        timeout=30
                    )

                    if response.status_code == 200:
                        result = response.json()

                        if result.get('code') == 200:
                            download_links = result.get('data', [])
                            if download_links and len(download_links) > 0:
                                download_url = download_links[0].get('urls', [''])[0]
                                if download_url:
                                    self.log(f"     ✅ 解析成功")

                                    # 存储解析出的链接
                                    self.parsed_links.append({
                                        'filename': filename,
                                        'url': download_url,
                                        'size': file_info.get('size', 0),
                                        'path': file_path,
                                        'folder': folder_name
                                    })

                                    # 调用IDM下载到指定文件夹
                                    if self.auto_download_var.get():
                                        if self.call_idm_download_to_folder(download_url, filename, folder_download_path):
                                            self.log(f"     📥 已发送到IDM")
                                        else:
                                            self.log(f"     ⚠️  IDM调用失败")

                                    success_count += 1
                                else:
                                    self.log(f"     ❌ 未获取到下载链接")
                            else:
                                self.log(f"     ❌ 下载链接数据为空")
                        else:
                            self.log(f"     ❌ {result.get('message', '解析失败')}")
                    else:
                        self.log(f"     ❌ HTTP {response.status_code}")

                except Exception as e:
                    self.log(f"     ❌ 解析出错: {str(e)}")

                # 添加小延迟避免请求过快
                import time
                time.sleep(0.5)

            return success_count

        except Exception as e:
            self.log(f"❌ 下载文件夹文件出错: {str(e)}")
            return 0



    def parse_files_batch(self, files_list):
        """批量解析文件下载链接"""
        try:
            url = self.url_entry.get().strip()
            pwd = self.pwd_entry.get().strip()
            token = self.token_entry.get().strip()

            base_url = "https://dp.wpurl.cc/api/v1/user/parse"

            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Content-Type": "application/json",
                "Accept": "application/json"
            }

            success_count = 0
            failed_count = 0

            for i, file_info in enumerate(files_list, 1):
                filename = file_info.get('server_filename', '')
                file_path = file_info.get('path', '')

                # 更新进度
                self.show_progress(i-1, len(files_list), f"正在解析: {filename}")
                self.log(f"🔗 [{i}/{len(files_list)}] 正在解析: {filename}")

                try:
                    rand1, rand2, rand3 = self.generate_rand_params()

                    # 获取文件所在的目录路径
                    file_dir = '/'.join(file_path.split('/')[:-1]) or '/'

                    download_data = {
                        "token": token,
                        "url": url,
                        "pwd": pwd,
                        "dir": file_dir,
                        "fs_id": [file_info.get('fs_id')],
                        "randsk": self.randsk or '',
                        "uk": self.uk or '',
                        "shareid": self.shareid or '',
                        "surl": url.split('/')[-1].replace('?', ''),
                        "parse_password": "",
                        "vcode_str": "",
                        "vcode_input": "",
                        "rand1": rand1,
                        "rand2": rand2,
                        "rand3": rand3
                    }

                    response = requests.post(
                        f"{base_url}/get_download_links",
                        headers=headers,
                        json=download_data,
                        timeout=30
                    )

                    if response.status_code == 200:
                        result = response.json()

                        if result.get('code') == 200:
                            download_links = result.get('data', [])
                            if download_links and len(download_links) > 0:
                                download_url = download_links[0].get('urls', [''])[0]
                                if download_url:
                                    self.log(f"   ✅ 解析成功")

                                    # 存储解析出的链接
                                    self.parsed_links.append({
                                        'filename': filename,
                                        'url': download_url,
                                        'size': file_info.get('size', 0),
                                        'path': file_path
                                    })

                                    # 自动调用IDM下载
                                    if self.auto_download_var.get():
                                        if self.call_idm_download(download_url, filename, file_path):
                                            self.log(f"   📥 已发送到IDM")
                                        else:
                                            self.log(f"   ⚠️  IDM调用失败")

                                    success_count += 1
                                else:
                                    self.log(f"   ❌ 未获取到下载链接")
                                    failed_count += 1
                            else:
                                self.log(f"   ❌ 下载链接数据为空")
                                failed_count += 1
                        else:
                            self.log(f"   ❌ {result.get('message', '解析失败')}")
                            failed_count += 1
                    else:
                        self.log(f"   ❌ HTTP {response.status_code}")
                        failed_count += 1

                except Exception as e:
                    self.log(f"   ❌ 解析出错: {str(e)}")
                    failed_count += 1

                # 添加小延迟避免请求过快
                import time
                time.sleep(0.5)

            # 隐藏进度条
            self.hide_progress()

            # 显示解析结果摘要
            self.log("=" * 50)
            self.log(f"📊 批量解析完成！")
            self.log(f"✅ 成功: {success_count} 个文件")
            self.log(f"❌ 失败: {failed_count} 个文件")

            if self.parsed_links:
                total_size = sum(link['size'] for link in self.parsed_links)
                self.log(f"📦 总大小: {self.format_size(total_size)}")

                if not self.auto_download_var.get():
                    self.log("💡 提示: 可以点击'复制所有链接'按钮复制下载链接")

                # 显示完成对话框
                messagebox.showinfo(
                    "解析完成",
                    f"批量解析完成！\n\n成功: {success_count} 个文件\n失败: {failed_count} 个文件\n总大小: {self.format_size(total_size)}"
                )
            else:
                messagebox.showwarning("解析失败", "没有成功解析出任何下载链接")

        except Exception as e:
            self.log(f"❌ 批量解析过程出错: {str(e)}")
            messagebox.showerror("错误", f"批量解析过程出错: {str(e)}")
            
    def parse_selected_files(self):
        """解析选中的文件"""
        selected_files = []

        for item in self.tree.get_children():
            if self.tree.item(item, "text") == "☑":
                values = self.tree.item(item, "values")
                if len(values) >= 5:
                    file_data_str = values[4]  # file_data在第5列
                    if file_data_str:
                        file_info = json.loads(file_data_str)
                        selected_files.append(file_info)
                    
        if not selected_files:
            messagebox.showwarning("警告", "请先选择要解析的文件")
            return
            
        def parse_files():
            try:
                self.parse_btn.config(state="disabled")
                self.parsed_links.clear()  # 清空之前的链接
                self.log(f"准备解析 {len(selected_files)} 个文件...")

                url = self.url_entry.get().strip()
                pwd = self.pwd_entry.get().strip()
                token = self.token_entry.get().strip()

                base_url = "https://dp.wpurl.cc/api/v1/user/parse"
                rand1, rand2, rand3 = self.generate_rand_params()

                headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                    "Content-Type": "application/json",
                    "Accept": "application/json"
                }

                for file_info in selected_files:
                    filename = file_info.get('server_filename', '')

                    if file_info.get('is_dir'):
                        self.log(f"跳过文件夹: {filename}")
                        continue

                    self.log(f"正在解析文件: {filename}")

                    # 获取下载链接的API调用
                    download_data = {
                        "token": token,
                        "url": url,
                        "pwd": pwd,
                        "dir": self.current_path,
                        "fs_id": [file_info.get('fs_id')],
                        "randsk": self.randsk or '',
                        "uk": self.uk or '',
                        "shareid": self.shareid or '',
                        "surl": url.split('/')[-1].replace('?', ''),
                        "parse_password": "",
                        "vcode_str": "",
                        "vcode_input": "",
                        "rand1": rand1,
                        "rand2": rand2,
                        "rand3": rand3
                    }

                    try:
                        response = requests.post(
                            f"{base_url}/get_download_links",
                            headers=headers,
                            json=download_data,
                            timeout=30
                        )

                        if response.status_code == 200:
                            result = response.json()
                            self.log(f"下载链接API响应: {result}")

                            if result.get('code') == 200:
                                download_links = result.get('data', [])
                                if download_links and len(download_links) > 0:
                                    download_url = download_links[0].get('urls', [''])[0]
                                    if download_url:
                                        self.log(f"✅ {filename}")
                                        self.log(f"   下载链接: {download_url}")
                                        self.log(f"   文件大小: {self.format_size(file_info.get('size', 0))}")

                                        # 存储解析出的链接
                                        self.parsed_links.append({
                                            'filename': filename,
                                            'url': download_url,
                                            'size': file_info.get('size', 0)
                                        })

                                        # 自动调用IDM下载
                                        if self.auto_download_var.get():
                                            if self.call_idm_download(download_url, filename):
                                                self.log(f"   ✅ 已发送到IDM下载")
                                            else:
                                                self.log(f"   ❌ IDM调用失败")

                                        self.log("   " + "="*50)
                                    else:
                                        self.log(f"❌ {filename}: 未获取到下载链接")
                                else:
                                    self.log(f"❌ {filename}: 下载链接数据为空")
                            else:
                                self.log(f"❌ {filename}: {result.get('message', '解析失败')}")
                        else:
                            self.log(f"❌ {filename}: HTTP {response.status_code}")

                    except Exception as e:
                        self.log(f"❌ {filename}: 解析出错 - {str(e)}")

                self.log("解析完成！")

                # 显示解析结果摘要
                if self.parsed_links:
                    self.log(f"📊 解析摘要: 成功解析 {len(self.parsed_links)} 个文件")
                    total_size = sum(link['size'] for link in self.parsed_links)
                    self.log(f"📦 总大小: {self.format_size(total_size)}")

                    if not self.auto_download_var.get():
                        self.log("💡 提示: 可以点击'复制所有链接'按钮复制下载链接")
                else:
                    self.log("❌ 没有成功解析出任何下载链接")

            except Exception as e:
                self.log(f"解析过程出错: {str(e)}")
                messagebox.showerror("错误", f"解析过程出错: {str(e)}")
            finally:
                self.parse_btn.config(state="normal")

        # 在新线程中执行解析
        threading.Thread(target=parse_files, daemon=True).start()

if __name__ == "__main__":
    root = tk.Tk()
    app = BaiduPanUI(root)
    root.mainloop()
