import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import requests
import secrets
import json
from datetime import datetime
import threading

class BaiduPanUI:
    def __init__(self, root):
        self.root = root
        self.root.title("百度网盘解析工具")
        self.root.geometry("1000x700")
        
        # 当前文件列表数据
        self.current_files = []
        self.current_path = "/"
        
        self.create_widgets()
        
    def create_widgets(self):
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
        # 输入区域
        ttk.Label(main_frame, text="分享链接:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.url_entry = ttk.Entry(main_frame, width=60)
        self.url_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=2)
        self.url_entry.insert(0, "https://pan.baidu.com/s/1mVe3LrL7US1MF5jICzmycg?")
        
        ttk.Label(main_frame, text="提取码:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.pwd_entry = ttk.Entry(main_frame, width=20)
        self.pwd_entry.grid(row=1, column=1, sticky=tk.W, pady=2)
        self.pwd_entry.insert(0, "1111")
        
        ttk.Label(main_frame, text="解析卡密:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.token_entry = ttk.Entry(main_frame, width=30)
        self.token_entry.grid(row=2, column=1, sticky=tk.W, pady=2)
        self.token_entry.insert(0, "thAfgmFnC0uahCK3")
        
        # 当前路径显示
        path_frame = ttk.Frame(main_frame)
        path_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        path_frame.columnconfigure(1, weight=1)
        
        ttk.Label(path_frame, text="当前路径:").grid(row=0, column=0, sticky=tk.W)
        self.path_label = ttk.Label(path_frame, text="/", background="white", relief="sunken")
        self.path_label.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(5, 0))
        
        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=2, sticky=tk.E, pady=5)
        
        self.get_list_btn = ttk.Button(button_frame, text="获取文件列表", command=self.get_file_list)
        self.get_list_btn.grid(row=0, column=0, padx=2)
        
        self.back_btn = ttk.Button(button_frame, text="返回上级", command=self.go_back, state="disabled")
        self.back_btn.grid(row=0, column=1, padx=2)
        
        # 文件列表区域
        list_frame = ttk.Frame(main_frame)
        list_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        # 创建Treeview
        columns = ("name", "type", "size", "mtime")
        self.tree = ttk.Treeview(list_frame, columns=columns, show="tree headings", height=15)
        
        # 设置列标题
        self.tree.heading("#0", text="选择")
        self.tree.heading("name", text="文件名")
        self.tree.heading("type", text="类型")
        self.tree.heading("size", text="大小")
        self.tree.heading("mtime", text="修改时间")
        
        # 设置列宽
        self.tree.column("#0", width=50, minwidth=50)
        self.tree.column("name", width=400, minwidth=200)
        self.tree.column("type", width=80, minwidth=80)
        self.tree.column("size", width=100, minwidth=100)
        self.tree.column("mtime", width=150, minwidth=150)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 绑定双击事件
        self.tree.bind("<Double-1>", self.on_item_double_click)
        
        # 操作按钮区域
        action_frame = ttk.Frame(main_frame)
        action_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        self.parse_btn = ttk.Button(action_frame, text="解析选中文件", command=self.parse_selected_files)
        self.parse_btn.grid(row=0, column=0, padx=2)
        
        self.select_all_btn = ttk.Button(action_frame, text="全选", command=self.select_all)
        self.select_all_btn.grid(row=0, column=1, padx=2)
        
        self.clear_selection_btn = ttk.Button(action_frame, text="清除选择", command=self.clear_selection)
        self.clear_selection_btn.grid(row=0, column=2, padx=2)
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="操作日志", padding="5")
        log_frame.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置主框架的行权重
        main_frame.rowconfigure(6, weight=1)
        
    def log(self, message):
        """添加日志信息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update()
        
    def generate_rand_params(self):
        """生成随机参数"""
        rand1 = secrets.token_hex(20)  # 40位十六进制
        rand2 = secrets.token_hex(16)  # 32位十六进制
        rand3 = secrets.token_hex(20)  # 40位十六进制
        return rand1, rand2, rand3
        
    def format_size(self, size_in_bytes):
        """将字节数转换为人类可读的格式"""
        if size_in_bytes == 0:
            return "文件夹"
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size_in_bytes < 1024.0:
                return f"{size_in_bytes:.2f} {unit}"
            size_in_bytes /= 1024.0
        return f"{size_in_bytes:.2f} PB"
        
    def format_time(self, timestamp):
        """将时间戳转换为可读格式"""
        return datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
        
    def get_file_list(self):
        """获取文件列表"""
        def fetch_files():
            try:
                self.get_list_btn.config(state="disabled")
                self.log("开始获取文件列表...")
                
                url = self.url_entry.get().strip()
                pwd = self.pwd_entry.get().strip()
                token = self.token_entry.get().strip()
                
                if not all([url, pwd, token]):
                    messagebox.showerror("错误", "请填写完整的分享链接、提取码和解析卡密")
                    return
                
                base_url = "https://dp.wpurl.cc/api/v1/user/parse"
                rand1, rand2, rand3 = self.generate_rand_params()
                
                headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                    "Content-Type": "application/json",
                    "Accept": "application/json"
                }
                
                file_list_data = {
                    "token": token,
                    "url": url,
                    "pwd": pwd,
                    "dir": self.current_path,
                    "surl": url.split('/')[-1].replace('?', ''),
                    "parse_password": "",
                    "rand1": rand1,
                    "rand2": rand2,
                    "rand3": rand3
                }
                
                self.log(f"请求路径: {self.current_path}")
                
                response = requests.post(
                    f"{base_url}/get_file_list",
                    headers=headers,
                    json=file_list_data,
                    timeout=30
                )
                
                if response.status_code == 200:
                    result = response.json()
                    self.log(f"API响应: {result}")
                    
                    if result.get('code') == 200:
                        files = result.get('data', {}).get('list', [])
                        self.current_files = files
                        self.update_file_list(files)
                        self.log(f"成功获取 {len(files)} 个文件/文件夹")
                    else:
                        self.log(f"API返回错误: {result.get('message', '未知错误')}")
                        messagebox.showerror("错误", f"获取文件列表失败: {result.get('message', '未知错误')}")
                else:
                    self.log(f"HTTP错误: {response.status_code}")
                    messagebox.showerror("错误", f"请求失败: HTTP {response.status_code}")
                    
            except Exception as e:
                self.log(f"获取文件列表出错: {str(e)}")
                messagebox.showerror("错误", f"获取文件列表出错: {str(e)}")
            finally:
                self.get_list_btn.config(state="normal")
                
        # 在新线程中执行网络请求
        threading.Thread(target=fetch_files, daemon=True).start()
        
    def update_file_list(self, files):
        """更新文件列表显示"""
        # 清空现有项目
        for item in self.tree.get_children():
            self.tree.delete(item)
            
        # 更新路径显示
        self.path_label.config(text=self.current_path)
        
        # 启用/禁用返回按钮
        self.back_btn.config(state="normal" if self.current_path != "/" else "disabled")
        
        # 添加文件项目
        for file_info in files:
            file_type = "📁 文件夹" if file_info.get('is_dir') else "📄 文件"
            size = self.format_size(file_info.get('size', 0))
            mtime = self.format_time(file_info.get('server_mtime', 0))
            
            item_id = self.tree.insert("", "end", 
                                     text="☐",  # 复选框符号
                                     values=(file_info.get('server_filename', ''), 
                                           file_type, size, mtime))
            
            # 存储文件信息到item
            self.tree.set(item_id, "file_data", json.dumps(file_info))
            
    def on_item_double_click(self, event):
        """处理双击事件"""
        item = self.tree.selection()[0] if self.tree.selection() else None
        if not item:
            return
            
        # 获取文件信息
        file_data_str = self.tree.set(item, "file_data")
        if not file_data_str:
            return
            
        file_info = json.loads(file_data_str)
        
        # 如果是文件夹，进入该文件夹
        if file_info.get('is_dir'):
            self.current_path = file_info.get('path', '/')
            self.get_file_list()
        else:
            # 如果是文件，切换选择状态
            current_text = self.tree.item(item, "text")
            if current_text == "☐":
                self.tree.item(item, text="☑")
            else:
                self.tree.item(item, text="☐")
                
    def go_back(self):
        """返回上级目录"""
        if self.current_path == "/":
            return
            
        # 计算上级路径
        path_parts = self.current_path.rstrip('/').split('/')
        if len(path_parts) > 1:
            self.current_path = '/'.join(path_parts[:-1]) or '/'
        else:
            self.current_path = '/'
            
        self.get_file_list()
        
    def select_all(self):
        """全选所有项目"""
        for item in self.tree.get_children():
            self.tree.item(item, text="☑")
            
    def clear_selection(self):
        """清除所有选择"""
        for item in self.tree.get_children():
            self.tree.item(item, text="☐")
            
    def parse_selected_files(self):
        """解析选中的文件"""
        selected_files = []
        
        for item in self.tree.get_children():
            if self.tree.item(item, "text") == "☑":
                file_data_str = self.tree.set(item, "file_data")
                if file_data_str:
                    file_info = json.loads(file_data_str)
                    selected_files.append(file_info)
                    
        if not selected_files:
            messagebox.showwarning("警告", "请先选择要解析的文件")
            return
            
        self.log(f"准备解析 {len(selected_files)} 个文件...")
        
        # 这里可以添加解析下载链接的逻辑
        for file_info in selected_files:
            filename = file_info.get('server_filename', '')
            if file_info.get('is_dir'):
                self.log(f"文件夹: {filename} (暂不支持文件夹解析)")
            else:
                self.log(f"文件: {filename} (解析功能待实现)")

if __name__ == "__main__":
    root = tk.Tk()
    app = BaiduPanUI(root)
    root.mainloop()
