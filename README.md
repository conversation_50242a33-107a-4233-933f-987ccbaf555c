# 百度网盘解析工具

一个功能强大的百度网盘分享链接解析和下载工具，支持文件夹批量下载和IDM集成。

## 功能特点

- ✅ **文件列表浏览**: 支持浏览百度网盘分享链接中的文件和文件夹
- ✅ **智能文件夹下载**: 选择文件夹自动创建同名目录并下载所有文件
- ✅ **IDM集成**: 自动调用IDM下载管理器进行高速下载
- ✅ **批量处理**: 支持同时选择多个文件夹进行批量下载
- ✅ **进度显示**: 实时显示扫描和解析进度
- ✅ **递归扫描**: 自动处理嵌套的子文件夹
- ✅ **链接复制**: 支持复制下载链接到剪贴板

## 打包方法

### 方法一：使用批处理文件（推荐）

1. 双击运行 `build_simple.bat`
2. 等待打包完成
3. 在 `dist` 目录中找到 `百度网盘解析工具.exe`

### 方法二：使用Python脚本

```bash
python build_config.py
```

### 方法三：手动打包

1. 安装依赖：
```bash
pip install pyinstaller requests
```

2. 执行打包命令：
```bash
pyinstaller --onefile --windowed --name "百度网盘解析工具" baidu_pan_ui.py
```

## 使用说明

### 基本操作

1. **输入信息**：
   - 分享链接：百度网盘分享链接
   - 提取码：分享链接的提取码
   - 解析卡密：第三方解析服务的卡密

2. **浏览文件**：
   - 点击"获取文件列表"浏览文件
   - 右键文件夹选择"进入文件夹"浏览内容
   - 使用"返回上级"按钮返回上级目录

3. **选择下载**：
   - 双击文件/文件夹进行选中（☐ → ☑）
   - 可以选择多个文件夹
   - 点击"下载选中文件夹"开始批量下载

### IDM设置

1. **自动检测**：程序会自动检测IDM安装路径
2. **手动设置**：如果检测失败，可以手动浏览选择IDM路径
3. **测试功能**：点击"测试IDM"验证IDM是否正常工作
4. **下载目录**：可以自定义下载保存目录

### 文件夹下载特点

- 🎯 **精确控制**：只下载选中的文件夹
- 📁 **自动分类**：每个文件夹创建独立的下载目录
- 🚀 **批量处理**：支持多文件夹同时下载
- 💾 **结构保持**：保持原有的文件夹层次结构

## 系统要求

- Windows 7/8/10/11
- Python 3.6+ (仅开发环境需要)
- 网络连接
- IDM (可选，用于高速下载)

## 注意事项

1. **网络连接**：首次运行需要网络连接验证卡密
2. **IDM安装**：如需高速下载功能，请先安装IDM
3. **卡密获取**：需要有效的第三方解析服务卡密
4. **磁盘空间**：确保有足够空间存储下载文件
5. **防火墙**：某些防火墙可能阻止程序调用IDM

## 文件结构

```
百度网盘解析工具/
├── baidu_pan_ui.py          # 主程序文件
├── requirements.txt         # 依赖列表
├── build_simple.bat        # 简单打包脚本
├── build_config.py         # 详细打包配置
├── README.md               # 说明文档
└── dist/                   # 打包输出目录
    └── 百度网盘解析工具.exe  # 可执行文件
```

## 常见问题

### Q: IDM无法启动怎么办？
A: 
1. 检查IDM是否正确安装
2. 手动设置IDM路径
3. 使用"测试IDM"功能验证
4. 可以使用"复制所有链接"功能手动下载

### Q: 解析失败怎么办？
A:
1. 检查网络连接
2. 验证分享链接和提取码是否正确
3. 确认卡密是否有效
4. 查看操作日志中的详细错误信息

### Q: 下载速度慢怎么办？
A:
1. 使用IDM下载管理器
2. 检查网络连接质量
3. 尝试更换下载时间段

## 更新日志

### v1.0.0
- 初始版本发布
- 支持文件列表浏览
- 支持文件夹批量下载
- 集成IDM下载功能
- 添加进度显示
- 支持链接复制功能

## 免责声明

本工具仅供学习和研究使用，请遵守相关法律法规和服务条款。使用本工具产生的任何后果由用户自行承担。

## 技术支持

如有问题或建议，请通过以下方式联系：
- 查看操作日志中的详细错误信息
- 确保网络连接正常
- 验证IDM安装和配置
