// ==UserScript==
// @name         百度网盘解析助手
// @namespace    http://tampermonkey.net/
// @version      0.1
// @description  百度网盘文件解析助手，支持获取直链
// <AUTHOR> name
// @match        *://pan.baidu.com/s/*
// @match        *://yun.baidu.com/s/*
// @grant        GM_xmlhttpRequest
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_registerMenuCommand
// @connect      dp.wpurl.cc
// ==/UserScript==

(function() {
    'use strict';

    // 样式注入
    const style = `
        .parse-btn {
            margin-left: 8px;
            padding: 6px 12px;
            color: white;
            background: #06a7ff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .parse-btn:hover {
            background: #0095e6;
        }
        .parse-dialog {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 9999;
            min-width: 400px;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
        }
        .parse-dialog h3 {
            margin: 0 0 16px 0;
            padding-bottom: 8px;
            border-bottom: 1px solid #eee;
        }
        .parse-dialog .close {
            position: absolute;
            right: 16px;
            top: 16px;
            cursor: pointer;
            color: #999;
            font-size: 18px;
        }
        .parse-dialog .close:hover {
            color: #666;
        }
        .parse-form {
            margin-bottom: 16px;
        }
        .parse-form input {
            width: 100%;
            padding: 8px;
            margin-bottom: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .parse-result {
            background: #f5f5f5;
            padding: 12px;
            border-radius: 4px;
            margin-top: 12px;
            white-space: pre-wrap;
            word-break: break-all;
        }
        .file-item {
            display: flex;
            align-items: center;
            padding: 8px;
            border-bottom: 1px solid #eee;
        }
        .file-item:last-child {
            border-bottom: none;
        }
        .file-name {
            flex: 1;
            margin-right: 12px;
        }
        .file-size {
            color: #666;
            margin-right: 12px;
        }
        .parse-link {
            color: #06a7ff;
            cursor: pointer;
            text-decoration: underline;
        }
    `;

    // 注入样式
    const styleElement = document.createElement('style');
    styleElement.textContent = style;
    document.head.appendChild(styleElement);

    // 生成随机参数
    function generateRandParams() {
        function randomHex(length) {
            const chars = '0123456789abcdef';
            let result = '';
            for (let i = 0; i < length; i++) {
                result += chars[Math.floor(Math.random() * chars.length)];
            }
            return result;
        }
        
        return {
            rand1: randomHex(40),
            rand2: randomHex(32),
            rand3: randomHex(40)
        };
    }

    // 格式化文件大小
    function formatSize(size) {
        if (size === 0) return '0 B';
        const units = ['B', 'KB', 'MB', 'GB', 'TB'];
        const k = 1024;
        const i = Math.floor(Math.log(size) / Math.log(k));
        return parseFloat((size / Math.pow(k, i)).toFixed(2)) + ' ' + units[i];
    }

    // 获取文件列表
    async function getFileList(token, url, pwd, dir = "/") {
        const surl = url.split('/').pop().replace('?', '');
        const { rand1, rand2, rand3 } = generateRandParams();
        
        const response = await fetch('https://dp.wpurl.cc/api/v1/user/parse/get_file_list', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            },
            body: JSON.stringify({
                token,
                url,
                pwd,
                dir,
                surl,
                parse_password: "",
                rand1,
                rand2,
                rand3
            })
        });

        return await response.json();
    }

    // 获取下载链接
    async function getDownloadLink(token, url, pwd, dir, fs_id, randsk, uk, shareid, surl) {
        const { rand1, rand2, rand3 } = generateRandParams();
        
        const response = await fetch('https://dp.wpurl.cc/api/v1/user/parse/get_download_links', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            },
            body: JSON.stringify({
                token,
                url,
                pwd,
                dir,
                fs_id: [fs_id],
                randsk,
                uk,
                shareid,
                surl,
                parse_password: "",
                vcode_str: "",
                vcode_input: "",
                rand1,
                rand2,
                rand3
            })
        });

        return await response.json();
    }

    // 创建解析按钮
    function createParseButton() {
        const downloadButton = document.querySelector('.download-btn, .g-button[title="下载"]');
        if (!downloadButton || document.querySelector('.parse-btn')) return;

        const parseButton = document.createElement('button');
        parseButton.className = 'parse-btn';
        parseButton.textContent = '解析下载';
        parseButton.onclick = showParseDialog;

        downloadButton.parentNode.insertBefore(parseButton, downloadButton.nextSibling);
    }

    // 创建解析对话框
    function createParseDialog() {
        const dialog = document.createElement('div');
        dialog.className = 'parse-dialog';
        dialog.innerHTML = `
            <div class="close">×</div>
            <h3>文件解析</h3>
            <div class="parse-form">
                <input type="text" placeholder="请输入解析卡密" id="parse-token">
                <button class="parse-confirm-btn">确认</button>
            </div>
            <div id="parse-result"></div>
        `;

        // 添加确认按钮样式
        const buttonStyle = `
            .parse-confirm-btn {
                width: 100%;
                padding: 8px;
                background: #06a7ff;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
                margin-top: 8px;
            }
            .parse-confirm-btn:hover {
                background: #0095e6;
            }
        `;
        const styleElement = document.createElement('style');
        styleElement.textContent = buttonStyle;
        document.head.appendChild(styleElement);

        document.body.appendChild(dialog);

        // 关闭按钮事件
        dialog.querySelector('.close').onclick = () => dialog.remove();

        // 从localStorage获取上次使用的卡密
        const savedToken = localStorage.getItem('parse_token');
        if (savedToken) {
            dialog.querySelector('#parse-token').value = savedToken;
        }

        // 确认按钮点击事件
        dialog.querySelector('.parse-confirm-btn').onclick = async () => {
            const tokenInput = dialog.querySelector('#parse-token');
            const resultDiv = dialog.querySelector('#parse-result');
            
            if (!tokenInput.value) {
                resultDiv.innerHTML = '请输入解析卡密';
                return;
            }

            try {
                // 保存卡密到localStorage
                localStorage.setItem('parse_token', tokenInput.value);

                resultDiv.innerHTML = '正在获取文件列表...';
                const url = location.href;
                const pwd = document.querySelector('.share-access-code input')?.value || '';
                const fileListResult = await getFileList(tokenInput.value, url, pwd);

                if (fileListResult.code !== 200) {
                    resultDiv.innerHTML = `获取文件列表失败: ${fileListResult.message}`;
                    return;
                }

                const { uk, shareid, randsk, list } = fileListResult.data;
                
                // 显示文件列表
                resultDiv.innerHTML = '<div id="file-list"></div>';
                const fileListDiv = resultDiv.querySelector('#file-list');

                for (const file of list) {
                    const fileItem = document.createElement('div');
                    fileItem.className = 'file-item';
                    
                    if (file.is_dir) {
                        // 如果是文件夹，添加展开按钮
                        fileItem.innerHTML = `
                            <span class="file-name">📁 ${file.server_filename}</span>
                            <span class="file-size">${formatSize(file.size)}</span>
                            <span class="parse-link" data-path="${file.path}">展开</span>
                        `;

                        // 点击展开文件夹
                        fileItem.querySelector('.parse-link').onclick = async () => {
                            const subList = await getFileList(tokenInput.value, url, pwd, file.path);
                            if (subList.code === 200) {
                                showSubFileList(subList.data.list, fileItem, tokenInput.value, url, pwd, randsk, uk, shareid);
                            }
                        };
                    } else {
                        // 如果是文件，添加解析按钮
                        fileItem.innerHTML = `
                            <span class="file-name">📄 ${file.server_filename}</span>
                            <span class="file-size">${formatSize(file.size)}</span>
                            <span class="parse-link" data-fs-id="${file.fs_id}">解析</span>
                        `;

                        // 点击解析文件
                        fileItem.querySelector('.parse-link').onclick = async () => {
                            const linkResult = await getDownloadLink(
                                tokenInput.value, 
                                url, 
                                pwd, 
                                file.path,
                                file.fs_id,
                                randsk,
                                uk,
                                shareid,
                                url.split('/').pop().replace('?', '')
                            );

                            if (linkResult.code === 200) {
                                const downloadUrl = linkResult.data[0].urls[0];
                                // 创建下载链接
                                const linkDiv = document.createElement('div');
                                linkDiv.innerHTML = `
                                    <div class="parse-result">
                                        <p>文件名：${file.server_filename}</p>
                                        <p>大小：${formatSize(file.size)}</p>
                                        <p>下载链接：<a href="${downloadUrl}" target="_blank">${downloadUrl}</a></p>
                                    </div>
                                `;
                                fileItem.appendChild(linkDiv);
                            } else {
                                alert(`解析失败: ${linkResult.message}`);
                            }
                        };
                    }

                    fileListDiv.appendChild(fileItem);
                }

            } catch (error) {
                resultDiv.innerHTML = `解析出错: ${error.message}`;
            }
        };

        return dialog;
    }

    // 获取分享链接和提取码
    function getShareInfo() {
        // 获取当前URL
        let url = location.href;
        
        // 处理短链接格式
        if (url.includes('/s/1')) {
            url = url.split('?')[0];  // 移除URL参数
        }
        
        // 获取提取码
        let pwd = '';
        // 尝试从URL参数获取提取码
        const urlParams = new URLSearchParams(location.search);
        if (urlParams.has('pwd')) {
            pwd = urlParams.get('pwd');
        } else {
            // 尝试从输入框获取提取码
            const pwdInput = document.querySelector('.share-access-code input, #accessCode');
            if (pwdInput) {
                pwd = pwdInput.value;
            }
        }
        
        return { url, pwd };
    }

    // 显示解析对话框
    async function showParseDialog() {
        const dialog = createParseDialog();
        const resultDiv = dialog.querySelector('#parse-result');
        const tokenInput = dialog.querySelector('#parse-token');

        // 从localStorage获取上次使用的卡密
        const savedToken = localStorage.getItem('parse_token');
        if (savedToken) {
            tokenInput.value = savedToken;
        }

        // 确认按钮点击事件
        dialog.querySelector('.parse-confirm-btn').onclick = async () => {
            if (!tokenInput.value) {
                resultDiv.innerHTML = '请输入解析卡密';
                return;
            }

            try {
                // 保存卡密到localStorage
                localStorage.setItem('parse_token', tokenInput.value);

                resultDiv.innerHTML = '正在获取文件列表...';
                
                // 获取分享信息
                const { url, pwd } = getShareInfo();
                
                if (!url) {
                    resultDiv.innerHTML = '无法获取分享链接，请确保当前页面是百度网盘分享页面';
                    return;
                }

                const fileListResult = await getFileList(tokenInput.value, url, pwd);

                if (fileListResult.code !== 200) {
                    resultDiv.innerHTML = `获取文件列表失败: ${fileListResult.message}`;
                    return;
                }

                const { uk, shareid, randsk, list } = fileListResult.data;
                
                // 显示文件列表
                resultDiv.innerHTML = '<div id="file-list"></div>';
                const fileListDiv = resultDiv.querySelector('#file-list');

                for (const file of list) {
                    const fileItem = document.createElement('div');
                    fileItem.className = 'file-item';
                    
                    if (file.is_dir) {
                        // 如果是文件夹，添加展开按钮
                        fileItem.innerHTML = `
                            <span class="file-name">📁 ${file.server_filename}</span>
                            <span class="file-size">${formatSize(file.size)}</span>
                            <span class="parse-link" data-path="${file.path}">展开</span>
                        `;

                        // 点击展开文件夹
                        fileItem.querySelector('.parse-link').onclick = async () => {
                            const subList = await getFileList(tokenInput.value, url, pwd, file.path);
                            if (subList.code === 200) {
                                showSubFileList(subList.data.list, fileItem, tokenInput.value, url, pwd, randsk, uk, shareid);
                            }
                        };
                    } else {
                        // 如果是文件，添加解析按钮
                        fileItem.innerHTML = `
                            <span class="file-name">📄 ${file.server_filename}</span>
                            <span class="file-size">${formatSize(file.size)}</span>
                            <span class="parse-link" data-fs-id="${file.fs_id}">解析</span>
                        `;

                        // 点击解析文件
                        fileItem.querySelector('.parse-link').onclick = async () => {
                            const linkResult = await getDownloadLink(
                                tokenInput.value, 
                                url, 
                                pwd, 
                                file.path,
                                file.fs_id,
                                randsk,
                                uk,
                                shareid,
                                url.split('/').pop().replace('?', '')
                            );

                            if (linkResult.code === 200) {
                                const downloadUrl = linkResult.data[0].urls[0];
                                // 创建下载链接
                                const linkDiv = document.createElement('div');
                                linkDiv.innerHTML = `
                                    <div class="parse-result">
                                        <p>文件名：${file.server_filename}</p>
                                        <p>大小：${formatSize(file.size)}</p>
                                        <p>下载链接：<a href="${downloadUrl}" target="_blank">${downloadUrl}</a></p>
                                    </div>
                                `;
                                fileItem.appendChild(linkDiv);
                            } else {
                                alert(`解析失败: ${linkResult.message}`);
                            }
                        };
                    }

                    fileListDiv.appendChild(fileItem);
                }

            } catch (error) {
                resultDiv.innerHTML = `解析出错: ${error.message}`;
            }
        };
    }

    // 显示子文件列表
    function showSubFileList(list, parentElement, token, url, pwd, randsk, uk, shareid) {
        const subListDiv = document.createElement('div');
        subListDiv.style.marginLeft = '20px';

        for (const file of list) {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';

            if (file.is_dir) {
                fileItem.innerHTML = `
                    <span class="file-name">📁 ${file.server_filename}</span>
                    <span class="file-size">${formatSize(file.size)}</span>
                    <span class="parse-link" data-path="${file.path}">展开</span>
                `;

                fileItem.querySelector('.parse-link').onclick = async () => {
                    const subList = await getFileList(token, url, pwd, file.path);
                    if (subList.code === 200) {
                        showSubFileList(subList.data.list, fileItem, token, url, pwd, randsk, uk, shareid);
                    }
                };
            } else {
                fileItem.innerHTML = `
                    <span class="file-name">📄 ${file.server_filename}</span>
                    <span class="file-size">${formatSize(file.size)}</span>
                    <span class="parse-link" data-fs-id="${file.fs_id}">解析</span>
                `;

                fileItem.querySelector('.parse-link').onclick = async () => {
                    const linkResult = await getDownloadLink(
                        token,
                        url,
                        pwd,
                        file.path,
                        file.fs_id,
                        randsk,
                        uk,
                        shareid,
                        url.split('/').pop().replace('?', '')
                    );

                    if (linkResult.code === 200) {
                        const downloadUrl = linkResult.data[0].urls[0];
                        const linkDiv = document.createElement('div');
                        linkDiv.innerHTML = `
                            <div class="parse-result">
                                <p>文件名：${file.server_filename}</p>
                                <p>大小：${formatSize(file.size)}</p>
                                <p>下载链接：<a href="${downloadUrl}" target="_blank">${downloadUrl}</a></p>
                            </div>
                        `;
                        fileItem.appendChild(linkDiv);
                    } else {
                        alert(`解析失败: ${linkResult.message}`);
                    }
                };
            }

            subListDiv.appendChild(fileItem);
        }

        parentElement.appendChild(subListDiv);
    }

    // 监听页面变化，添加解析按钮
    const observer = new MutationObserver(() => {
        createParseButton();
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    // 初始化
    createParseButton();
})(); 