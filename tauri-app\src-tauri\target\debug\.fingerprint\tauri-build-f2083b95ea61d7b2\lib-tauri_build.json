{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"codegen\", \"config-json5\", \"config-toml\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 16335202704479430798, "path": 13589022644760642780, "deps": [[4450062412064442726, "dirs_next", false, 13158494868361667006], [4899080583175475170, "semver", false, 9344693553595423202], [7468248713591957673, "cargo_toml", false, 11605589084950680182], [8292277814562636972, "tauri_utils", false, 7878931743930959709], [9689903380558560274, "serde", false, 3990983680525661946], [10301936376833819828, "json_patch", false, 17194258125140370292], [13077543566650298139, "heck", false, 1272924532536600590], [13625485746686963219, "anyhow", false, 16646634681217262864], [14189313126492979171, "tauri_winres", false, 10409608707808305377], [15367738274754116744, "serde_json", false, 7776261297850374089], [15622660310229662834, "walkdir", false, 12558466473541677370]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-f2083b95ea61d7b2\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}