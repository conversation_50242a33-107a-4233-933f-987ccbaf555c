{"rustc": 16591470773350601817, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"debug\", \"default\", \"simd\", \"std\", \"unstable-doc\", \"unstable-recover\"]", "target": 13376497836617006023, "profile": 12998675864389268938, "path": 1021655659502377690, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\winnow-79a409b4215fe273\\dep-lib-winnow", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}