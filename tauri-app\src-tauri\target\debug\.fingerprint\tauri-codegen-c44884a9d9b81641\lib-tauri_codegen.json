{"rustc": 16591470773350601817, "features": "[\"brotli\", \"compression\", \"regex\", \"shell-scope\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\", \"regex\", \"shell-scope\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 9838547495381030375, "deps": [[3060637413840920116, "proc_macro2", false, 17854706098805066986], [4899080583175475170, "semver", false, 9344693553595423202], [7392050791754369441, "ico", false, 9402550550770458983], [8008191657135824715, "thiserror", false, 10567539360728756001], [8292277814562636972, "tauri_utils", false, 7878931743930959709], [8319709847752024821, "uuid", false, 17329545268604777101], [9451456094439810778, "regex", false, 8907661920642779731], [9689903380558560274, "serde", false, 3990983680525661946], [9857275760291862238, "sha2", false, 15925050765793354038], [10301936376833819828, "json_patch", false, 17194258125140370292], [12687914511023397207, "png", false, 16588225433771134428], [14132538657330703225, "brotli", false, 12400560921152978072], [15367738274754116744, "serde_json", false, 7776261297850374089], [15622660310229662834, "walkdir", false, 12558466473541677370], [17990358020177143287, "quote", false, 11626565584918915378], [18066890886671768183, "base64", false, 5118330385245696460]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-c44884a9d9b81641\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}