{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[]", "target": 13151518555585256095, "profile": 15657897354478470176, "path": 16428551681595344794, "deps": [[1462335029370885857, "quick_xml", false, 896860861547284248], [3334271191048661305, "windows_version", false, 13086222162247402031], [10806645703491011684, "thiserror", false, 18171882269777459243], [13116089016666501665, "windows", false, 11763555127702408962]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-winrt-notification-3f98198630ec1438\\dep-lib-tauri_winrt_notification", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}