[package]
name = "baidu-pan-parser"
version = "1.0.0"
description = "百度网盘解析工具"
authors = ["Your Name"]
license = "MIT"
repository = ""
default-run = "baidu-pan-parser"
edition = "2021"
rust-version = "1.60"

[build-dependencies]
tauri-build = { version = "1.5.0", features = [] }

[dependencies]
serde_json = "1.0"
serde = { version = "1.0", features = ["derive"] }
tauri = { version = "1.5.0", features = [ "fs-remove-file", "dialog-open", "fs-read-dir", "fs-create-dir", "shell-open", "clipboard-all", "fs-write-file", "fs-exists", "http-all", "fs-rename-file", "fs-read-file", "notification-all", "fs-remove-dir", "dialog-save"] }
reqwest = { version = "0.11", features = ["json"] }
tokio = { version = "1.0", features = ["full"] }
uuid = { version = "1.0", features = ["v4"] }

[features]
default = ["custom-protocol"]
custom-protocol = ["tauri/custom-protocol"]
