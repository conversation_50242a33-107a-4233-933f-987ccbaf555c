import React from 'react';
import { Folder, File, CheckSquare, Square } from 'lucide-react';

interface FileInfo {
  fs_id: number;
  is_dir: boolean;
  server_filename: string;
  size: number;
  path: string;
  server_mtime: number;
}

interface FileListProps {
  files: FileInfo[];
  selectedFiles: Set<number>;
  onToggleSelection: (fsId: number) => void;
  onEnterFolder: (path: string) => void;
  formatSize: (bytes: number) => string;
  formatTime: (timestamp: number) => string;
}

export const FileList: React.FC<FileListProps> = ({
  files,
  selectedFiles,
  onToggleSelection,
  onEnterFolder,
  formatSize,
  formatTime,
}) => {
  const handleDoubleClick = (file: FileInfo) => {
    onToggleSelection(file.fs_id);
  };

  const handleRightClick = (e: React.MouseEvent, file: FileInfo) => {
    e.preventDefault();
    if (file.is_dir) {
      onEnterFolder(file.path);
    }
  };

  if (files.length === 0) {
    return (
      <div className="card">
        <div className="card-content">
          <div className="text-center py-12 text-gray-500">
            <Folder className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>暂无文件，请先获取文件列表</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-semibold">文件列表</h3>
        <p className="text-sm text-gray-600">共 {files.length} 个项目</p>
      </div>
      <div className="card-content">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-medium text-gray-700">选择</th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">文件名</th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">类型</th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">大小</th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">修改时间</th>
              </tr>
            </thead>
            <tbody>
              {files.map((file) => (
                <tr
                  key={file.fs_id}
                  className="border-b border-gray-100 hover:bg-gray-50 transition-colors"
                  onDoubleClick={() => handleDoubleClick(file)}
                  onContextMenu={(e) => handleRightClick(e, file)}
                >
                  <td className="py-3 px-4">
                    <button
                      onClick={() => onToggleSelection(file.fs_id)}
                      className="text-primary-600 hover:text-primary-700"
                    >
                      {selectedFiles.has(file.fs_id) ? (
                        <CheckSquare className="w-5 h-5" />
                      ) : (
                        <Square className="w-5 h-5" />
                      )}
                    </button>
                  </td>
                  <td className="py-3 px-4">
                    <div className="flex items-center space-x-3">
                      {file.is_dir ? (
                        <Folder className="w-5 h-5 text-blue-500" />
                      ) : (
                        <File className="w-5 h-5 text-gray-500" />
                      )}
                      <span className="font-medium text-gray-900">
                        {file.server_filename}
                      </span>
                    </div>
                  </td>
                  <td className="py-3 px-4">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      {file.is_dir ? '📁 文件夹' : '📄 文件'}
                    </span>
                  </td>
                  <td className="py-3 px-4 text-gray-600">
                    {formatSize(file.size)}
                  </td>
                  <td className="py-3 px-4 text-gray-600">
                    {formatTime(file.server_mtime)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};
