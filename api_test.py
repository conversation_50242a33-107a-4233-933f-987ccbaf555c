import requests
import secrets
import json
from datetime import datetime

def format_size(size_in_bytes):
    """将字节数转换为人类可读的格式"""
    if size_in_bytes == 0:
        return "文件夹"
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if size_in_bytes < 1024.0:
            return f"{size_in_bytes:.2f} {unit}"
        size_in_bytes /= 1024.0
    return f"{size_in_bytes:.2f} PB"

def format_time(timestamp):
    """将时间戳转换为可读格式"""
    return datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')

def generate_rand_params():
    # 生成随机的十六进制字符串
    # rand1 和 rand3 需要40位十六进制字符 (20字节)
    # rand2 需要32位十六进制字符 (16字节)
    rand1 = secrets.token_hex(20)  # 40位十六进制
    rand2 = secrets.token_hex(16)  # 32位十六进制
    rand3 = secrets.token_hex(20)  # 40位十六进制
    return rand1, rand2, rand3

def query_token_info(token_to_query):
    base_url = "https://dp.wpurl.cc/api/v1/user/token"
    
    rand1, rand2, rand3 = generate_rand_params()
    
    params = {
        "token": token_to_query,
        "rand1": rand1,
        "rand2": rand2,
        "rand3": rand3
    }
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    }
    
    print(f"查询卡密信息: {token_to_query}")
    
    response = requests.get(base_url, params=params, headers=headers)
    
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        print("响应JSON:")
        response_json = response.json()
        print(json.dumps(response_json, indent=4, ensure_ascii=False))
    else:
        print("错误响应:")
        print(response.text)

def parse_bdpan_file(token, share_url, pwd, current_dir="/"):
    """
    解析百度网盘分享链接
    
    Args:
        token (str): 解析卡密
        share_url (str): 百度网盘分享链接
        pwd (str): 提取码
        current_dir (str): 当前目录路径
    """
    base_url = "https://dp.wpurl.cc/api/v1/user/parse"
    rand1, rand2, rand3 = generate_rand_params()
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    # 获取文件列表
    print(f"\n获取目录 {current_dir} 的文件列表...")
    file_list_data = {
        "token": token,
        "url": share_url,
        "pwd": pwd,
        "dir": current_dir,
        "surl": share_url.split('/')[-1],
        "parse_password": "",
        "rand1": rand1,
        "rand2": rand2,
        "rand3": rand3
    }
    
    file_list_response = requests.post(
        f"{base_url}/get_file_list",
        headers=headers,
        json=file_list_data
    )
    
    if file_list_response.status_code != 200:
        print("获取文件列表失败:")
        print(file_list_response.text)
        return
    
    response_json = file_list_response.json()
    print("\n文件列表响应:")
    print(json.dumps(response_json, indent=4, ensure_ascii=False))
    
    try:
        files = response_json['data']['list']
        print(f"\n在 {current_dir} 中找到 {len(files)} 个文件/文件夹:")
        print("=" * 100)
        print(f"{'类型':<8} {'名称':<50} {'大小':<15} {'修改时间':<20}")
        print("=" * 100)
        
        # 先显示文件夹
        for file in sorted(files, key=lambda x: (not x['is_dir'], x['server_filename'])):
            file_type = "📁" if file['is_dir'] else "📄"
            size = format_size(file['size'])
            mtime = format_time(file['server_mtime'])
            name = file['server_filename']
            if len(name) > 48:
                name = name[:45] + "..."
            
            print(f"{file_type:<8} {name:<50} {size:<15} {mtime:<20}")
            
            # 如果是文件夹，递归获取其内容
            if file['is_dir']:
                parse_bdpan_file(token, share_url, pwd, file['path'])
        
        print("=" * 100)
        print()
        
    except KeyError as e:
        print(f"解析文件列表失败: {e}")
        print("原始响应:", file_list_response.text)
        return

if __name__ == "__main__":
    # 测试卡密
    test_token = "thAfgmFnC0uahCK3"
    
    # 1. 查询卡密信息
    print("=" * 50)
    print("测试卡密信息查询:")
    print("=" * 50)
    query_token_info(test_token)
    
    # 2. 测试文件解析
    print("\n" + "=" * 50)
    print("测试文件解析:")
    print("=" * 50)
    test_share_url = "https://pan.baidu.com/s/1mVe3LrL7US1MF5jICzmycg?"
    test_pwd = "1111"
    parse_bdpan_file(test_token, test_share_url, test_pwd)
    