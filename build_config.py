#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
百度网盘解析工具打包配置
"""

import os
import sys
import subprocess

def check_dependencies():
    """检查依赖是否安装"""
    dependencies = ['requests', 'pyinstaller']
    missing = []
    
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"✅ {dep} 已安装")
        except ImportError:
            missing.append(dep)
            print(f"❌ {dep} 未安装")
    
    if missing:
        print(f"\n正在安装缺失的依赖: {', '.join(missing)}")
        for dep in missing:
            subprocess.run([sys.executable, '-m', 'pip', 'install', dep])
    
    return len(missing) == 0

def build_executable():
    """构建可执行文件"""
    print("\n🚀 开始打包...")
    
    # PyInstaller 命令参数
    cmd = [
        'pyinstaller',
        '--onefile',                    # 打包成单个文件
        '--windowed',                   # 不显示控制台窗口
        '--name', '百度网盘解析工具',      # 可执行文件名
        '--distpath', 'dist',           # 输出目录
        '--workpath', 'build',          # 临时文件目录
        '--specpath', '.',              # spec文件位置
        '--clean',                      # 清理临时文件
        'baidu_pan_ui.py'              # 主程序文件
    ]
    
    # 添加隐藏导入（如果需要）
    hidden_imports = [
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.filedialog',
        'tkinter.scrolledtext',
        'requests',
        'json',
        'threading',
        'subprocess',
        'os',
        'secrets',
        'datetime'
    ]
    
    for imp in hidden_imports:
        cmd.extend(['--hidden-import', imp])
    
    # 执行打包命令
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 打包成功！")
        print(f"📁 可执行文件位置: dist{os.sep}百度网盘解析工具.exe")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("🔧 百度网盘解析工具打包程序")
    print("=" * 50)
    
    # 检查依赖
    print("\n📦 检查依赖...")
    if not check_dependencies():
        print("❌ 依赖安装失败，请手动安装")
        return
    
    # 构建可执行文件
    if build_executable():
        print("\n🎉 打包完成！")
        print("\n📋 使用说明:")
        print("1. 可执行文件位于 dist 目录中")
        print("2. 首次运行请确保网络连接正常")
        print("3. 如需IDM下载功能，请先安装IDM")
        print("4. 程序会自动检测IDM安装路径")
        print("\n💡 提示: 可以将exe文件复制到任意位置运行")
    else:
        print("❌ 打包失败，请检查错误信息")

if __name__ == "__main__":
    main()
