{"build": {"beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build", "devPath": "http://localhost:1420", "distDir": "../dist", "withGlobalTauri": false}, "package": {"productName": "百度网盘解析工具", "version": "1.0.0"}, "tauri": {"allowlist": {"all": false, "shell": {"all": false, "open": true}, "dialog": {"all": false, "open": true, "save": true}, "fs": {"all": false, "readFile": true, "writeFile": true, "readDir": true, "createDir": true, "removeDir": true, "removeFile": true, "renameFile": true, "exists": true}, "http": {"all": true, "request": true}, "notification": {"all": true}, "clipboard": {"all": true, "readText": true, "writeText": true}}, "bundle": {"active": true, "targets": "all", "identifier": "com.baidupan.parser", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}, "security": {"csp": null}, "windows": [{"fullscreen": false, "resizable": true, "title": "百度网盘解析工具", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600}]}}