# 百度网盘解析工具 - Tauri版

基于Tauri + React + TypeScript + Tailwind CSS构建的现代化百度网盘解析工具。

## 功能特点

- 🚀 **高性能**: Rust后端 + React前端，体积小、速度快
- 🎨 **现代化UI**: 使用Tailwind CSS构建的美观界面
- 📁 **文件夹下载**: 支持选择文件夹批量下载
- 🔗 **IDM集成**: 自动调用IDM进行高速下载
- 📊 **实时日志**: 详细的操作日志和进度显示
- ⚙️ **灵活配置**: 可自定义IDM路径和下载目录

## 技术栈

- **前端**: React 18 + TypeScript + Tailwind CSS
- **后端**: Rust + Tauri
- **构建工具**: Vite
- **图标**: Lucide React

## 开发环境要求

- Node.js 16+
- Rust 1.60+
- 操作系统: Windows/macOS/Linux

## 安装和运行

### 1. 安装依赖

```bash
# 安装前端依赖
npm install

# 安装Tauri CLI (如果还没安装)
npm install -g @tauri-apps/cli
```

### 2. 开发模式运行

```bash
npm run tauri dev
```

### 3. 构建生产版本

```bash
npm run tauri build
```

## 项目结构

```
tauri-app/
├── src/                    # React前端源码
│   ├── components/         # React组件
│   │   ├── FileList.tsx   # 文件列表组件
│   │   ├── SettingsPanel.tsx # 设置面板
│   │   └── LogPanel.tsx   # 日志面板
│   ├── App.tsx            # 主应用组件
│   ├── main.tsx           # React入口
│   └── index.css          # 样式文件
├── src-tauri/             # Rust后端源码
│   ├── src/
│   │   └── main.rs        # Rust主程序
│   ├── Cargo.toml         # Rust依赖配置
│   └── tauri.conf.json    # Tauri配置
├── package.json           # Node.js依赖
├── vite.config.ts         # Vite配置
├── tailwind.config.js     # Tailwind配置
└── tsconfig.json          # TypeScript配置
```

## 主要功能

### 1. 文件列表浏览
- 输入百度网盘分享链接和提取码
- 浏览文件夹结构
- 支持多级目录导航

### 2. 文件选择和下载
- 双击选中文件/文件夹
- 支持批量选择
- 右键进入文件夹

### 3. IDM集成
- 自动检测IDM安装路径
- 支持手动配置IDM路径
- 一键测试IDM功能

### 4. 实时日志
- 详细的操作日志
- 实时显示处理进度
- 错误信息提示

## API接口

Rust后端提供以下API接口：

- `get_file_list`: 获取文件列表
- `get_download_links`: 获取下载链接
- `call_idm_download`: 调用IDM下载

## 与Python版本对比

| 特性 | Python版本 | Tauri版本 |
|------|------------|-----------|
| 体积 | ~50-100MB | ~10-20MB |
| 性能 | 中等 | 高 |
| UI现代化 | 一般 | 优秀 |
| 内存占用 | 高 | 低 |
| 启动速度 | 慢 | 快 |
| 跨平台 | 好 | 优秀 |

## 注意事项

1. 首次运行需要网络连接
2. 需要有效的解析卡密
3. IDM功能需要先安装IDM
4. 确保有足够的磁盘空间

## 开发说明

### 添加新功能

1. 前端组件放在 `src/components/` 目录
2. Rust API函数添加到 `src-tauri/src/main.rs`
3. 使用 `#[tauri::command]` 标注Rust函数
4. 在前端使用 `invoke()` 调用Rust函数

### 样式开发

项目使用Tailwind CSS，支持：
- 响应式设计
- 深色模式 (可扩展)
- 自定义组件样式

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！
